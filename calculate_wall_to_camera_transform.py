"""
计算从墙的三维坐标系到摄像头三维坐标系的变换矩阵

假设条件：
- 摄像头pitch角度：17°
- 摄像头yaw角度：-2°
- 摄像头和墙的距离：1米
- 摄像头中心和墙的三维坐标系原点的水平距离：0.1米

坐标系定义：
- 墙坐标系：原点在墙面上，Z轴垂直墙面向外，X轴水平向右，Y轴垂直向上
- 摄像头坐标系：原点在摄像头光心，Z轴沿光轴向前，X轴向右，Y轴向下
"""

import numpy as np
import math

def rot_x(deg: float) -> np.ndarray:
    """绕X轴旋转矩阵"""
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    """绕Y轴旋转矩阵"""
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    """绕Z轴旋转矩阵"""
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    """构建旋转矩阵：R = Rz(roll) * Ry(yaw) * Rx(pitch)"""
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

def calculate_wall_to_camera_transform():
    """
    计算从墙坐标系到摄像头坐标系的变换矩阵

    假设条件：
    - 摄像头光心到墙原点的直线距离为1米
    - pitch角度向上倾斜为正数
    - 摄像头中心相对墙原点有0.1米的水平偏移

    返回：
    - T_wall_to_cam: 4x4变换矩阵，将墙坐标系中的点变换到摄像头坐标系
    """

    # 摄像头参数
    pitch_cam = 17.0    # 摄像头pitch角度（度，向上倾斜为正）
    yaw_cam = -2.0      # 摄像头yaw角度（度）
    roll_cam = 0.0      # 假设roll角度为0

    # 几何参数
    direct_distance = 1.0       # 摄像头光心到墙原点的直线距离（米）
    horizontal_offset = 0.1     # 摄像头中心相对墙坐标系原点的水平距离（米）

    # 1. 根据直线距离和角度计算摄像头在墙坐标系中的位置
    # 摄像头光心到墙原点的直线距离为1米，同时考虑水平偏移
    # 需要确保 ||C_cam - wall_origin|| = direct_distance = 1.0米

    # 设摄像头位置为 [horizontal_offset, y, z]
    # 约束条件：horizontal_offset² + y² + z² = direct_distance²
    # 同时考虑pitch角度的几何关系

    pitch_rad = math.radians(pitch_cam)

    # 在没有水平偏移的情况下：
    # z = -direct_distance * cos(pitch)
    # y = -direct_distance * sin(pitch)

    # 有水平偏移时，需要调整以保持直线距离为1米
    # 使用几何关系：x² + y² + z² = 1²
    remaining_distance_sq = direct_distance**2 - horizontal_offset**2
    remaining_distance = math.sqrt(remaining_distance_sq)

    # 在YZ平面内分配剩余距离
    z_offset = -remaining_distance * math.cos(pitch_rad)
    y_offset = -remaining_distance * math.sin(pitch_rad)

    C_cam_in_wall = np.array([horizontal_offset, y_offset, z_offset])
    
    # 2. 摄像头的姿态（相对于墙坐标系）
    # 摄像头的camera-to-world旋转矩阵（从摄像头坐标系到墙坐标系）
    R_cam_to_wall = build_rotation(pitch_cam, yaw_cam, roll_cam)
    
    # 3. 从墙坐标系到摄像头坐标系的变换
    # 旋转矩阵：R_wall_to_cam = R_cam_to_wall^T
    R_wall_to_cam = R_cam_to_wall.T
    
    # 平移向量：t_wall_to_cam = -R_wall_to_cam * C_cam_in_wall
    t_wall_to_cam = -R_wall_to_cam @ C_cam_in_wall
    
    # 4. 构建4x4变换矩阵
    T_wall_to_cam = np.eye(4)
    T_wall_to_cam[:3, :3] = R_wall_to_cam
    T_wall_to_cam[:3, 3] = t_wall_to_cam
    
    return T_wall_to_cam, R_wall_to_cam, t_wall_to_cam, C_cam_in_wall

def print_transform_matrix(T, R, t, C_cam):
    """打印变换矩阵的详细信息"""
    print("=" * 60)
    print("从墙坐标系到摄像头坐标系的变换矩阵计算")
    print("=" * 60)

    print(f"\n摄像头参数：")
    print(f"  Pitch角度: 17.0° (向上倾斜为正)")
    print(f"  Yaw角度: -2.0°")
    print(f"  Roll角度: 0.0°")

    print(f"\n几何参数：")
    print(f"  摄像头光心到墙原点的直线距离: 1.0米")
    print(f"  水平偏移: 0.1米")

    print(f"\n摄像头在墙坐标系中的位置：")
    print(f"  C_cam = [{C_cam[0]:.3f}, {C_cam[1]:.3f}, {C_cam[2]:.3f}]")

    # 验证直线距离
    wall_origin = np.array([0.0, 0.0, 0.0])
    actual_distance = np.linalg.norm(C_cam - wall_origin)
    print(f"  验证直线距离: {actual_distance:.3f}米")
    
    print(f"\n旋转矩阵 R_wall_to_cam (3x3)：")
    for i in range(3):
        print(f"  [{R[i,0]:8.5f}, {R[i,1]:8.5f}, {R[i,2]:8.5f}]")
    
    print(f"\n平移向量 t_wall_to_cam：")
    print(f"  [{t[0]:8.5f}, {t[1]:8.5f}, {t[2]:8.5f}]")
    
    print(f"\n完整变换矩阵 T_wall_to_cam (4x4)：")
    for i in range(4):
        print(f"  [{T[i,0]:8.5f}, {T[i,1]:8.5f}, {T[i,2]:8.5f}, {T[i,3]:8.5f}]")
    
    print(f"\n使用方法：")
    print(f"  对于墙坐标系中的点 P_wall = [x, y, z, 1]^T")
    print(f"  摄像头坐标系中的点 P_cam = T_wall_to_cam * P_wall")

def test_transform():
    """测试变换矩阵"""
    T, _, _, _ = calculate_wall_to_camera_transform()

    # 测试几个点
    print(f"\n测试点变换：")

    # 墙坐标系原点
    P_wall_origin = np.array([0, 0, 0, 1])
    P_cam_origin = T @ P_wall_origin
    print(f"  墙原点 [0,0,0] -> 摄像头坐标系: [{P_cam_origin[0]:.3f}, {P_cam_origin[1]:.3f}, {P_cam_origin[2]:.3f}]")

    # 墙上的一个点
    P_wall_point = np.array([0.5, 0.3, 0, 1])
    P_cam_point = T @ P_wall_point
    print(f"  墙上点 [0.5,0.3,0] -> 摄像头坐标系: [{P_cam_point[0]:.3f}, {P_cam_point[1]:.3f}, {P_cam_point[2]:.3f}]")

def create_visualization_data():
    """创建可视化数据，生成墙面网格点和变换后的点"""
    T, _, _, C_cam = calculate_wall_to_camera_transform()

    # 在墙面上创建网格点
    x_range = np.linspace(-0.5, 0.5, 11)  # -0.5m 到 0.5m
    y_range = np.linspace(-0.3, 0.3, 7)   # -0.3m 到 0.3m

    wall_points = []
    cam_points = []

    for x in x_range:
        for y in y_range:
            # 墙面上的点 (z=0)
            P_wall = np.array([x, y, 0, 1])
            wall_points.append(P_wall[:3])

            # 变换到摄像头坐标系
            P_cam = T @ P_wall
            cam_points.append(P_cam[:3])

    return np.array(wall_points), np.array(cam_points), T, C_cam

def analyze_transformation():
    """分析变换的几何特性"""
    print("\n" + "="*60)
    print("变换几何特性分析")
    print("="*60)

    _, R, _, _ = calculate_wall_to_camera_transform()

    # 分析旋转矩阵的特性
    det_R = np.linalg.det(R)
    print(f"\n旋转矩阵行列式: {det_R:.6f} (应该接近1.0)")

    # 检查是否为正交矩阵
    RTR = R.T @ R
    identity_error = np.max(np.abs(RTR - np.eye(3)))
    print(f"正交性检查 (R^T * R - I 的最大误差): {identity_error:.2e}")

    # 分析欧拉角
    # 从旋转矩阵反推欧拉角 (ZYX顺序)
    sy = math.sqrt(R[0,0]**2 + R[1,0]**2)
    singular = sy < 1e-6

    if not singular:
        x = math.atan2(R[2,1], R[2,2])
        y = math.atan2(-R[2,0], sy)
        z = math.atan2(R[1,0], R[0,0])
    else:
        x = math.atan2(-R[1,2], R[1,1])
        y = math.atan2(-R[2,0], sy)
        z = 0

    print(f"\n从变换矩阵反推的欧拉角:")
    print(f"  Roll (绕X轴):  {math.degrees(x):7.3f}°")
    print(f"  Pitch (绕Y轴): {math.degrees(y):7.3f}°")
    print(f"  Yaw (绕Z轴):   {math.degrees(z):7.3f}°")

    # 分析摄像头光轴方向
    # 摄像头坐标系中的Z轴 [0,0,1] 在墙坐标系中的方向
    cam_z_axis = R.T @ np.array([0, 0, 1])
    print(f"\n摄像头光轴在墙坐标系中的方向向量: [{cam_z_axis[0]:.3f}, {cam_z_axis[1]:.3f}, {cam_z_axis[2]:.3f}]")

    # 计算光轴与墙面法线的夹角
    wall_normal = np.array([0, 0, 1])  # 墙面法线
    cos_angle = np.dot(cam_z_axis, wall_normal)
    angle_deg = math.degrees(math.acos(abs(cos_angle)))
    print(f"摄像头光轴与墙面法线的夹角: {angle_deg:.2f}°")

if __name__ == "__main__":
    # 计算变换矩阵
    T_wall_to_cam, R_wall_to_cam, t_wall_to_cam, C_cam_in_wall = calculate_wall_to_camera_transform()

    # 打印结果
    print_transform_matrix(T_wall_to_cam, R_wall_to_cam, t_wall_to_cam, C_cam_in_wall)

    # 测试变换
    test_transform()

    # 分析变换特性
    analyze_transformation()

    # 创建可视化数据
    wall_pts, cam_pts, T, C_cam = create_visualization_data()
    print(f"\n生成了 {len(wall_pts)} 个测试点用于验证变换")
    print(f"墙面点范围: X[{wall_pts[:,0].min():.1f}, {wall_pts[:,0].max():.1f}], Y[{wall_pts[:,1].min():.1f}, {wall_pts[:,1].max():.1f}]")
    print(f"变换后点范围: X[{cam_pts[:,0].min():.3f}, {cam_pts[:,0].max():.3f}], Y[{cam_pts[:,1].min():.3f}, {cam_pts[:,1].max():.3f}], Z[{cam_pts[:,2].min():.3f}, {cam_pts[:,2].max():.3f}]")
