# 墙坐标系到摄像头坐标系变换矩阵

## 问题参数
- **摄像头pitch角度**: 17°
- **摄像头yaw角度**: -2°  
- **摄像头到墙距离**: 1.0米
- **水平偏移**: 0.1米

## 最终变换矩阵

### 4×4齐次变换矩阵 T_wall_to_cam

```
[ 0.99939,  0.00000,  0.03490, -0.06504]
[-0.01020,  0.95630,  0.29219,  0.29321]
[-0.03337, -0.29237,  0.95572,  0.95906]
[ 0.00000,  0.00000,  0.00000,  1.00000]
```

### 旋转矩阵 R (3×3)

```
[ 0.99939,  0.00000,  0.03490]
[-0.01020,  0.95630,  0.29219]
[-0.03337, -0.29237,  0.95572]
```

### 平移向量 t

```
[-0.06504, 0.29321, 0.95906]
```

## 使用方法

对于墙坐标系中的点 **P_wall = [x, y, z, 1]ᵀ**，转换到摄像头坐标系：

```
P_cam = T_wall_to_cam × P_wall
```

## 验证结果

- **旋转矩阵行列式**: 1.000000 ✓
- **正交性误差**: 2.22e-16 ✓  
- **摄像头光轴与墙面夹角**: 17.11° (符合17°pitch角)

## 测试示例

| 墙坐标系 | 摄像头坐标系 |
|---------|-------------|
| [0, 0, 0] | [-0.065, 0.293, 0.959] |
| [0.5, 0.3, 0] | [0.435, 0.575, 0.855] |

## 物理意义

1. **Z坐标为正**: 墙面在摄像头前方
2. **Y坐标偏移**: 17°pitch角导致的垂直偏移
3. **X坐标变化**: -2°yaw角和0.1米水平偏移的综合效果

这个变换矩阵可以准确地将墙面上的任意点坐标转换到摄像头坐标系中。
