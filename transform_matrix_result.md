# 墙坐标系到摄像头坐标系变换矩阵（垂直距离版）

## 问题参数
- **摄像头pitch角度**: 17° (向上倾斜为正)
- **摄像头yaw角度**: -2°
- **摄像头光心到墙的垂直距离**: 1.0米
- **水平偏移**: 0.1米

## 摄像头位置
摄像头在墙坐标系中的位置：**[0.100, -0.306, -1.000]**
- 验证垂直距离：**1.000米** ✓
- 到墙原点的直线距离：**1.050米**

## 最终变换矩阵

### 4×4齐次变换矩阵 T_wall_to_cam

```
[ 0.99939,  0.00000,  0.03490, -0.06504]
[-0.01020,  0.95630,  0.29219,  0.58559]
[-0.03337, -0.29237,  0.95572,  0.86967]
[ 0.00000,  0.00000,  0.00000,  1.00000]
```

### 旋转矩阵 R (3×3)

```
[ 0.99939,  0.00000,  0.03490]
[-0.01020,  0.95630,  0.29219]
[-0.03337, -0.29237,  0.95572]
```

### 平移向量 t

```
[-0.06504, 0.58559, 0.86967]
```

## 使用方法

对于墙坐标系中的点 **P_wall = [x, y, z, 1]ᵀ**，转换到摄像头坐标系：

```
P_cam = T_wall_to_cam × P_wall
```

## 验证结果

- **旋转矩阵行列式**: 1.000000 ✓
- **正交性误差**: 2.22e-16 ✓  
- **摄像头光轴与墙面夹角**: 17.11° (符合17°pitch角)

## 测试示例

| 墙坐标系 | 摄像头坐标系 |
|---------|-------------|
| [0, 0, 0] | [-0.065, 0.586, 0.870] |
| [0.5, 0.3, 0] | [0.435, 0.867, 0.765] |

## 关键特点

1. **垂直距离约束**: 摄像头光心到墙的垂直距离精确为1.0米
2. **正确的pitch角定义**: 向上倾斜为正数（17°）
3. **几何关系**: 由于pitch角，摄像头需要在Y方向偏移-0.306米

## 物理意义

1. **Z坐标为正**: 墙面在摄像头前方
2. **Y坐标为正**: 由于摄像头向上倾斜17°，墙面在摄像头坐标系中显示为向上
3. **X坐标变化**: -2°yaw角和0.1米水平偏移的综合效果
4. **Z坐标约0.87米**: 这是墙原点在摄像头光轴方向上的投影距离

## 几何关系

- **垂直距离**: 1.000米（Z坐标绝对值）✓
- **到墙原点直线距离**: 1.050米
- **Y偏移**: -0.306米 = -1.0 × tan(17°) ≈ -0.306米 ✓
- **摄像头光轴与墙面夹角**: 17.11° (符合17°pitch角) ✓

## 几何验证

- **旋转矩阵行列式**: 1.000000 ✓
- **正交性误差**: 2.22e-16 ✓
- **垂直距离验证**: 1.000米 ✓

这个变换矩阵基于垂直距离约束，可以准确地将墙面上的任意点坐标转换到摄像头坐标系中。
