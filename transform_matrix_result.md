# 墙坐标系到摄像头坐标系变换矩阵（修正版）

## 问题参数
- **摄像头pitch角度**: 17° (向上倾斜为正)
- **摄像头yaw角度**: -2°
- **摄像头光心到墙原点的直线距离**: 1.0米
- **水平偏移**: 0.1米

## 摄像头位置
摄像头在墙坐标系中的位置：**[0.100, -0.291, -0.952]**
- 验证直线距离：**1.000米** ✓

## 最终变换矩阵

### 4×4齐次变换矩阵 T_wall_to_cam

```
[ 0.99939,  0.00000,  0.03490, -0.06673]
[-0.01020,  0.95630,  0.29219,  0.55724]
[-0.03337, -0.29237,  0.95572,  0.82767]
[ 0.00000,  0.00000,  0.00000,  1.00000]
```

### 旋转矩阵 R (3×3)

```
[ 0.99939,  0.00000,  0.03490]
[-0.01020,  0.95630,  0.29219]
[-0.03337, -0.29237,  0.95572]
```

### 平移向量 t

```
[-0.06673, 0.55724, 0.82767]
```

## 使用方法

对于墙坐标系中的点 **P_wall = [x, y, z, 1]ᵀ**，转换到摄像头坐标系：

```
P_cam = T_wall_to_cam × P_wall
```

## 验证结果

- **旋转矩阵行列式**: 1.000000 ✓
- **正交性误差**: 2.22e-16 ✓  
- **摄像头光轴与墙面夹角**: 17.11° (符合17°pitch角)

## 测试示例

| 墙坐标系 | 摄像头坐标系 |
|---------|-------------|
| [0, 0, 0] | [-0.067, 0.557, 0.828] |
| [0.5, 0.3, 0] | [0.433, 0.839, 0.723] |

## 关键改进

1. **精确距离约束**: 摄像头光心到墙原点的直线距离精确为1.0米
2. **正确的pitch角定义**: 向上倾斜为正数（17°）
3. **几何一致性**: 考虑了水平偏移对距离约束的影响

## 物理意义

1. **Z坐标为正**: 墙面在摄像头前方
2. **Y坐标为正**: 由于摄像头向上倾斜17°，墙面在摄像头坐标系中显示为向上
3. **X坐标变化**: -2°yaw角和0.1米水平偏移的综合效果
4. **Z坐标约0.83米**: 这是墙原点在摄像头光轴方向上的投影距离

## 几何验证

- **旋转矩阵行列式**: 1.000000 ✓
- **正交性误差**: 2.22e-16 ✓
- **摄像头光轴与墙面夹角**: 17.11° (符合17°pitch角) ✓
- **直线距离验证**: 1.000米 ✓

这个变换矩阵可以准确地将墙面上的任意点坐标转换到摄像头坐标系中。
